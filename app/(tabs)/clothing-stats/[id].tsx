import { useState, useRef, useEffect } from 'react';
import { View, TouchableOpacity, SafeAreaView, Text, ScrollView, ActivityIndicator, StatusBar, Alert } from 'react-native';
import { Stack, useLocalSearchParams, useRouter } from 'expo-router';
import styled from 'styled-components/native';
import { ArrowLeft, MoreVertical } from 'lucide-react-native';
import { useItemById, useWearItem } from '@/methods/items';
import { deleteItem, ItemsUpdate, UploadImage, uploadImageToS3 } from '@/methods/cloths';
import WearItModal from './WearItModal';
import DeleteOptionsModal from './DeleteOptionsModal';
import DeleteItemModal from '@/components/DeleteItemModal';
import { Image } from 'expo-image';
import useResponsive from '@/hooks/useResponsive';
import { PADDING, FONT_SIZES, BORDER_RADIUS, HEIGHTS, isTablet } from '@/constants/responsive';
import { useQueryClient } from '@tanstack/react-query';
import { brand } from '@/constants/Colors';
import * as ImagePicker from 'expo-image-picker';

// Styled components
const SafeArea = styled(SafeAreaView)`
  flex: 1;
  background-color: #fff;
  padding-top: 0;
`;

const Container = styled(ScrollView)`
  flex: 1;
`;

const Header = styled(View)`
  flex-direction: row;
  align-items: center;
  padding: ${PADDING.CONTAINER_HORIZONTAL}px;
  margin-top: 1px; /* Reduced to match Closet layout */
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
`;

const BackButton = styled(TouchableOpacity)`
  width: ${isTablet() ? 56 : 40}px;
  height: ${isTablet() ? 56 : 40}px;
  border-radius: ${isTablet() ? 28 : 20}px;
  background-color: #F5F5F5;
  justify-content: center;
  align-items: center;
`;

const HeaderTitle = styled(Text)`
  font-family: CormorantGaramondSemiBold;
  font-size: ${FONT_SIZES.HEADER}px;
  line-height: ${isTablet() ? 40 : 32}px;
  color: #0E7E61;
  text-align: center;
  flex: 1;
  margin-right: 16px; /* Space between title and options button */
`;

const OptionsButton = styled(TouchableOpacity)`
  width: ${isTablet() ? 56 : 40}px;
  height: ${isTablet() ? 56 : 40}px;
  border-radius: ${isTablet() ? 28 : 20}px;
  background-color: #F5F5F5;
  justify-content: center;
  align-items: center;
`;

const ContentContainer = styled(View)<{ width: number }>`
  width: ${(props: { width: number }) => props.width}px;
  align-self: center;
  margin-top: ${isTablet() ? 110 : 70}px; /* Adjusted to match the new header position */
  gap: ${isTablet() ? 60 : 40}px;
  padding-bottom: ${isTablet() ? 80 : 40}px;
`;

const ImageContainer = styled(View)<{ size: number }>`
  width: ${(props: { size: number }) => props.size}px;
  height: ${(props: { size: number }) => props.size}px;
  background-color: #EBEBEB;
  border-radius: 10px;
  overflow: hidden;
  align-self: center;
  margin-top: ${PADDING.FIELD_VERTICAL}px;
  margin-bottom: ${isTablet() ? 30 : 20}px;
  /* We'll use a simple border instead of shadows for cross-platform consistency */
  border: 1px solid rgba(0, 0, 0, 0.1);
  position: relative; /* For positioning the loading indicator */
`;

const ItemImage = styled(Image)<{ size: number }>`
  width: ${(props: { size: number }) => props.size}px;
  height: ${(props: { size: number }) => props.size}px;
  border-radius: 8px;
`;

const ImageLoadingContainer = styled(View)`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  justify-content: center;
  align-items: center;
  background-color: rgba(240, 240, 240, 0.7);
  border-radius: 8px;
`;

const ImageOverlay = styled(View)`
  position: absolute;
  bottom: 8px;
  right: 8px;
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 12px;
  padding: 4px 8px;
`;

const ImageOverlayText = styled(Text)`
  color: white;
  font-family: 'MuktaVaani';
  font-size: ${isTablet() ? 12 : 10}px;
  font-weight: 500;
`;

// Define a blurhash placeholder for better loading experience
const PLACEHOLDER_BLURHASH = "L6PZfSi_.AyE_3t7t7R**0o#DgR4";

const ButtonContainer = styled(View)<{ width: number }>`
  width: ${(props: { width: number }) => props.width}px;
  height: ${HEIGHTS.INPUT}px;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  gap: ${PADDING.CONTAINER_HORIZONTAL}px;
`;

const TableContainer = styled(View)<{ width: number }>`
  width: ${(props: { width: number }) => props.width}px;
  flex-direction: column;
  align-items: flex-start;
  gap: ${PADDING.CONTAINER_HORIZONTAL}px;
`;

const TableTitle = styled(Text)`
  font-family: CormorantGaramondSemiBold;
  font-size: ${FONT_SIZES.HEADER}px;
  line-height: ${isTablet() ? 40 : 32}px;
  color: #333333;
`;

const StatsTable = styled(View)<{ width: number }>`
  width: ${(props: { width: number }) => props.width}px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 0px;
  background-color: #F0F0F0; /* Main table background */
  border: 1px solid #C0C0C0;
  border-radius: ${BORDER_RADIUS.FIELD_GROUP / 4}px;
  overflow: hidden;
  margin-top: ${PADDING.FIELD_VERTICAL / 2}px;
  box-sizing: border-box;
  /* Fix for border collapse */
  border-collapse: collapse;
`;

interface TableRowProps {
  height?: number;
  width?: number;
}

const TableRow = styled(View)<TableRowProps>`
  width: ${(props: TableRowProps) => props.width || '100%'};
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  padding: 0px;
  background-color: rgba(255, 255, 255, 0.0001); /* #FFFFFF00 with 0.01% opacity */
  height: ${(props: TableRowProps) => props.height || (isTablet() ? 60 : HEIGHTS.INPUT)}px;
  align-self: stretch;
  flex-wrap: nowrap; /* Prevent wrapping of cells */
  border-collapse: collapse; /* Ensure borders collapse properly */
`;

const TableCell = styled(View)<{ cellWidth?: number }>`
  width: ${(props: { cellWidth?: number }) => props.cellWidth ? `${props.cellWidth}px` : '50%'};
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  padding: 0px;
  background-color: rgba(255, 255, 255, 0.0001); /* #FFFFFF00 with 0.01% opacity */
  border-width: 1px;
  border-style: solid;
  border-color: #C0C0C0;
  flex: 1;
  align-self: stretch;
  overflow: hidden;
`;

const CellContent = styled(View)<{ cellWidth?: number }>`
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: ${PADDING.FIELD_VERTICAL / 2}px ${PADDING.FIELD_HORIZONTAL / 2}px;
  width: ${(props: { cellWidth?: number }) => props.cellWidth ? `${props.cellWidth}px` : '100%'};
  align-self: stretch;
  flex-grow: 0;
`;

const CellTitle = styled(Text)`
  font-family: 'MuktaVaaniSemiBold';
  font-style: normal;
  font-size: ${FONT_SIZES.LABEL - 2}px;
  line-height: ${isTablet() ? 24 : 16}px;
  color: #333333;
  flex-grow: 1;
  flex-shrink: 1;
  width: 100%;
`;

const CellText = styled(Text)`
  font-family: 'MuktaVaani';
  font-style: normal;
  font-size: ${FONT_SIZES.LABEL - 2}px;
  line-height: ${isTablet() ? 24 : 16}px;
  color: #333333;
  flex-grow: 1;
  flex-shrink: 1;
  width: 100%;
`;

const OutfitContainer = styled(View)<{ width: number }>`
  width: ${(props: { width: number }) => props.width}px;
  flex-direction: column;
  align-items: flex-start;
  gap: ${PADDING.CONTAINER_HORIZONTAL}px;
`;

const OutfitTitle = styled(Text)`
  font-family: CormorantGaramondSemiBold;
  font-size: ${FONT_SIZES.HEADER}px;
  line-height: ${isTablet() ? 40 : 32}px;
  color: #333333;
`;

const OutfitCardContainer = styled(View)<{ width: number, cardSize: number }>`
  width: ${(props: { width: number, cardSize: number }) => props.width}px;
  height: ${(props: { width: number, cardSize: number }) => props.cardSize}px;
  flex-direction: row;
  align-items: center;
  gap: ${PADDING.CONTAINER_HORIZONTAL}px;
`;

const OutfitCard = styled(View)<{ size: number }>`
  width: ${(props: { size: number }) => props.size}px;
  height: ${(props: { size: number }) => props.size}px;
  background-color: #EBEBEB;
  border-radius: 10px;
  justify-content: center;
  align-items: center;
  border: 1px solid rgba(0, 0, 0, 0.1);
  overflow: hidden;
  position: relative; /* For positioning the loading indicator */
`;

const OutfitImage = styled(Image)<{ size: number }>`
  width: ${(props: { size: number }) => props.size}px;
  height: ${(props: { size: number }) => props.size}px;
  border-radius: 8px;
`;

const SuggestedContainer = styled(View)<{ width: number }>`
  width: ${(props: { width: number }) => props.width}px;
  flex-direction: column;
  align-items: flex-start;
  gap: ${PADDING.CONTAINER_HORIZONTAL}px;
`;

const SuggestedTitle = styled(Text)`
  font-family: CormorantGaramondSemiBold;
  font-size: ${FONT_SIZES.HEADER}px;
  line-height: ${isTablet() ? 40 : 32}px;
  color: #333333;
`;

const SuggestedCardContainer = styled(View)<{ width: number, cardSize: number }>`
  width: ${(props: { width: number, cardSize: number }) => props.width}px;
  height: ${(props: { width: number, cardSize: number }) => props.cardSize}px;
  flex-direction: row;
  align-items: center;
  gap: ${PADDING.CONTAINER_HORIZONTAL}px;
`;

const SuggestedCard = styled(View)<{ size: number }>`
  width: ${(props: { size: number }) => props.size}px;
  height: ${(props: { size: number }) => props.size}px;
  background-color: #EBEBEB;
  border-radius: 10px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  overflow: hidden;
  justify-content: center;
  align-items: center;
  position: relative; /* For positioning the loading indicator */
`;

const SuggestedImage = styled(Image)<{ size: number }>`
  width: ${(props: { size: number }) => props.size}px;
  height: ${(props: { size: number }) => props.size}px;
  border-radius: 8px;
`;

const LoadingContainer = styled(View)`
  flex: 1;
  justify-content: center;
  align-items: center;
  margin-top: ${isTablet() ? 80 : 40}px;
`;

// Define placeholder image
const placeholderImage = require('../../../assets/images/placeholder-item.png');

// Utility function to optimize image URL for faster loading
const optimizeImageUrl = (url: string | null): string | null => {
  if (!url) return null;

  try {
    // If URL already contains optimization parameters, return as is
    if (url.includes('quality=') || url.includes('width=')) return url;

    // Parse the URL
    const urlObj = new URL(url);

    // Add quality parameter for faster loading (adjust as needed)
    urlObj.searchParams.append('quality', '85');

    // Add width parameter for appropriate sizing
    urlObj.searchParams.append('width', '600');

    // Return the optimized URL
    return urlObj.toString();
  } catch (error) {
    console.error('Error optimizing image URL:', error);
    return url;
  }
};

// Function to preload images
const preloadImages = async (urls: (string | null)[]): Promise<void> => {
  const validUrls = urls.filter(url => url !== null) as string[];

  if (validUrls.length === 0) return;

  try {
    console.log(`Preloading ${validUrls.length} images...`);
    await Image.prefetch(validUrls);
    console.log('Images preloaded successfully');
  } catch (error) {
    console.error('Error preloading images:', error);
  }
};

export default function ClothingStatsScreen() {
  const router = useRouter();
  const params = useLocalSearchParams();
  const id = params.id as string;
  const queryClient = useQueryClient();

  // Get responsive values
  const { width, isTablet } = useResponsive();

  // Calculate responsive dimensions
  const contentWidth = isTablet ? Math.min(PADDING.FRAME_CONTAINER_WIDTH + 100, width * 0.85) : width * 0.9;
  const imageSize = isTablet ? 380 : 240;
  const cardSize = isTablet ? 140 : 96;

  // Add state for image loading and error handling
  const [imageError, setImageError] = useState(false);
  const [mainImageLoading, setMainImageLoading] = useState(true);
  const [outfitImagesLoading, setOutfitImagesLoading] = useState<{[key: string]: boolean}>({});
  const [suggestedImagesLoading, setSuggestedImagesLoading] = useState<{[key: string]: boolean}>({});

  // State for WearItModal
  const [isWearItModalVisible, setIsWearItModalVisible] = useState(false);

  // State for Delete functionality
  const [isDeleteOptionsVisible, setIsDeleteOptionsVisible] = useState(false);
  const [isDeleteModalVisible, setIsDeleteModalVisible] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  // State for Image Update functionality
  const [isUpdatingImage, setIsUpdatingImage] = useState(false);
  const [newImageBase64, setNewImageBase64] = useState<string>('');
  const [newImageUri, setNewImageUri] = useState<string>('');

  // Track component mount state
  const isMounted = useRef(true);

  // Timeout reference for loading states
  const loadingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Use Tanstack Query to fetch item data
  const { data: item = {} as any, isLoading, error } = useItemById(id);

  // Log the item data for debugging
  useEffect(() => {
    if (item && Object.keys(item).length > 0) {
      console.log('Item data in component:', JSON.stringify(item, null, 2));
    }
  }, [item]);

  // Use Tanstack Query mutation for updating wear count
  const wearItemMutation = useWearItem();

  // Create the delete mutation
  const deleteItemMutation = deleteItem();

  // Create mutations for image updating
  const uploadImageMutation = UploadImage();
  const uploadImageToS3Mutation = uploadImageToS3();
  const updateItemMutation = ItemsUpdate();

  // Get the image URL (already formatted by the hook) and optimize it
  const imageUrl = optimizeImageUrl(item?.imageUrl || null);

  // Preload images when component mounts
  useEffect(() => {
    if (item) {
      const imagesToPreload: (string | null)[] = [
        imageUrl,
        // Add outfit images if available
        ...(item.outfits?.slice(0, 3).map((outfit: any) => optimizeImageUrl(outfit.imageUrl)) || []),
        // Add suggested pairing images if available
        ...(item.suggestedPairings?.slice(0, 3).map((pairing: any) => optimizeImageUrl(pairing.imageUrl)) || [])
      ];

      preloadImages(imagesToPreload);
    }
  }, [item]);

  // Log any errors
  if (error) {
    console.error('Error fetching item:', error);
  }

  // Handle component unmount
  useEffect(() => {
    return () => {
      isMounted.current = false;

      // Clear any timeouts to prevent memory leaks
      if (loadingTimeoutRef.current) {
        clearTimeout(loadingTimeoutRef.current);
        loadingTimeoutRef.current = null;
      }
    };
  }, []);

  // Delete handlers
  const handleOptionsPress = () => {
    setIsDeleteOptionsVisible(true);
  };

  const handleDeletePress = () => {
    setIsDeleteOptionsVisible(false);
    setIsDeleteModalVisible(true);
  };

  const confirmDeleteItem = async () => {
    if (!item._id) return;

    setIsDeleting(true);
    try {
      console.log('Deleting clothing item:', item.name);
      await deleteItemMutation.mutateAsync(item._id);

      // Show success message
      Alert.alert('Success', 'Item deleted successfully');

      // Refresh the clothes list
      queryClient.invalidateQueries({ queryKey: ['clothes'] });

      // Navigate back to closet
      router.replace('/(tabs)/closet');
    } catch (error) {
      console.error('Error deleting item:', error);
      Alert.alert('Error', 'Failed to delete item. Please try again.');
    } finally {
      setIsDeleting(false);
      setIsDeleteModalVisible(false);
    }
  };

  // Image update handlers
  const handleImagePress = () => {
    Alert.alert(
      'Update Photo',
      'Choose how you want to update this item\'s photo',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Photo Library',
          onPress: () => pickImageFromGallery(),
        },
        {
          text: 'Camera',
          onPress: () => pickImageFromCamera(),
        },
      ]
    );
  };

  const pickImageFromGallery = async () => {
    try {
      // Request permissions
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();

      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Please allow access to your photo library to select images.');
        return;
      }

      // Launch image library
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ['images'],
        allowsEditing: false,
        base64: true,
        aspect: [4, 3],
        quality: 1,
      });

      if (!result.canceled && result.assets?.[0]) {
        const selectedImage = result.assets[0];
        setNewImageBase64(selectedImage.base64 || '');
        setNewImageUri(selectedImage.uri);

        // Start the image update process
        updateItemImage(selectedImage.base64 || '');
      }
    } catch (error) {
      console.error('Error picking image from gallery:', error);
      Alert.alert('Error', 'Failed to access photo library. Please try again.');
    }
  };

  const pickImageFromCamera = async () => {
    try {
      // Request camera permissions
      const { status } = await ImagePicker.requestCameraPermissionsAsync();

      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Please allow camera access to take photos.');
        return;
      }

      // Launch camera
      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ['images'],
        allowsEditing: false,
        base64: true,
        aspect: [4, 3],
        quality: 1,
      });

      if (!result.canceled && result.assets?.[0]) {
        const selectedImage = result.assets[0];
        setNewImageBase64(selectedImage.base64 || '');
        setNewImageUri(selectedImage.uri);

        // Start the image update process
        updateItemImage(selectedImage.base64 || '');
      }
    } catch (error) {
      console.error('Error taking photo:', error);
      Alert.alert('Error', 'Failed to access camera. Please try again.');
    }
  };

  const updateItemImage = async (base64Image: string) => {
    if (!item._id || !base64Image) return;

    setIsUpdatingImage(true);

    try {
      console.log('Starting image update process for item:', item.name);

      // Step 1: Get pre-signed URL for image upload
      const uploadResponse = await uploadImageMutation.mutateAsync({
        imageUrl: base64Image, // This parameter is kept for compatibility but not used
        fileName: `item-${item._id}-${Date.now()}`,
        fileType: 'image/jpeg',
        folderPath: 'items',
      });

      console.log('Pre-signed URL obtained:', uploadResponse);

      // Step 2: Upload image to S3
      await uploadImageToS3Mutation.mutateAsync({
        imageUrl: base64Image,
        preSignedUrl: uploadResponse.preSignedURL,
      });

      console.log('Image uploaded to S3 successfully');

      // Step 3: Update item with new image URL
      await updateItemMutation.mutateAsync({
        itemId: item._id,
        imageUrl: uploadResponse.fileURL,
        // Preserve other item properties
        name: item.name,
        itemCategoryId: item.itemCategoryId,
        color: item.color,
        brand: item.brand,
      });

      console.log('Item updated with new image URL');

      // Refresh the item data
      queryClient.invalidateQueries({ queryKey: ['item', id] });
      queryClient.invalidateQueries({ queryKey: ['clothes'] });

      // Show success message
      Alert.alert('Success', 'Photo updated successfully!');

      // Clear temporary image states
      setNewImageBase64('');
      setNewImageUri('');

    } catch (error) {
      console.error('Error updating item image:', error);
      Alert.alert('Error', 'Failed to update photo. Please try again.');

      // Clear temporary image states on error
      setNewImageBase64('');
      setNewImageUri('');
    } finally {
      setIsUpdatingImage(false);
    }
  };

  return (
    <SafeArea>
      {/* Set status bar to match Closet screen */}
      <StatusBar backgroundColor="#FFFFFF" barStyle="dark-content" />

      {/* Force hide the header */}
      <Stack.Screen
        options={{
          headerShown: false,
          presentation: 'modal',
          animation: 'slide_from_bottom',
        }}
      />

      <Container>
        <Header>
          <BackButton onPress={() => router.replace('/(tabs)/closet')} activeOpacity={0.7}>
            <ArrowLeft size={isTablet ? 32 : 24} color="#333" />
          </BackButton>
          <HeaderTitle numberOfLines={2} ellipsizeMode="tail">
            {item.name || 'Item Details'}
          </HeaderTitle>
          <OptionsButton onPress={handleOptionsPress} activeOpacity={0.7}>
            <MoreVertical size={isTablet ? 28 : 20} color={brand.green[500]} />
          </OptionsButton>
        </Header>

        {isLoading ? (
          <LoadingContainer>
            <ActivityIndicator size="large" color="#0E7E61" />
            <Text
              style={{
                marginTop: isTablet ? 16 : 10,
                fontSize: isTablet ? 22 : 16,
                color: '#0E7E61',
                fontFamily: 'MuktaVaaniSemiBold',
                fontWeight: '600',
                textAlignVertical: 'center',
                includeFontPadding: false
              }}
            >
              Loading item details...
            </Text>
          </LoadingContainer>
        ) : (
          <ContentContainer width={contentWidth}>
            {/* Item Image - Tappable to update */}
            <TouchableOpacity
              onPress={handleImagePress}
              activeOpacity={0.8}
              disabled={isUpdatingImage}
            >
              <ImageContainer size={imageSize}>
                <ItemImage
                  size={imageSize}
                  source={
                    // Show new image if being updated, otherwise show current image
                    newImageUri ? { uri: newImageUri } :
                    imageUrl && !imageError ? { uri: imageUrl } : placeholderImage
                  }
                  placeholder={PLACEHOLDER_BLURHASH}
                  contentFit="cover"
                  transition={300}
                  cachePolicy="memory-disk"
                  recyclingKey={newImageUri ? `new-${Date.now()}` : id}
                  priority
                  onLoadStart={() => {
                    if (isMounted.current) {
                      setMainImageLoading(true);

                      // Set a timeout to ensure loading state doesn't get stuck
                      if (loadingTimeoutRef.current) {
                        clearTimeout(loadingTimeoutRef.current);
                      }

                      loadingTimeoutRef.current = setTimeout(() => {
                        if (isMounted.current) {
                          setMainImageLoading(false);
                          console.log('Image loading timeout triggered');
                        }
                      }, 5000); // 5 second timeout
                    }
                  }}
                  onLoad={() => {
                    if (isMounted.current) {
                      // Clear the timeout since the image loaded successfully
                      if (loadingTimeoutRef.current) {
                        clearTimeout(loadingTimeoutRef.current);
                        loadingTimeoutRef.current = null;
                      }

                      // Add a small delay to ensure the image is fully rendered
                      setTimeout(() => {
                        setMainImageLoading(false);
                      }, 200);
                    }
                  }}
                  onError={() => {
                    console.error('Image loading error');
                    if (isMounted.current) {
                      // Clear the timeout since we already know there's an error
                      if (loadingTimeoutRef.current) {
                        clearTimeout(loadingTimeoutRef.current);
                        loadingTimeoutRef.current = null;
                      }

                      setMainImageLoading(false);
                      setImageError(true);
                    }
                  }}
                />
                {(mainImageLoading || isUpdatingImage) && (
                  <ImageLoadingContainer>
                    <ActivityIndicator size="large" color="#0E7E61" />
                  </ImageLoadingContainer>
                )}

                {/* Overlay hint that image is tappable */}
                {!mainImageLoading && !isUpdatingImage && (
                  <ImageOverlay>
                    <ImageOverlayText>Tap to change</ImageOverlayText>
                  </ImageOverlay>
                )}
              </ImageContainer>
            </TouchableOpacity>

            {/* Button Container */}
            <ButtonContainer width={contentWidth}>
              <TouchableOpacity
                activeOpacity={0.7}
                onPress={() => {
                  console.log('Wear It button pressed for item ID:', id);
                  setIsWearItModalVisible(true);
                }}
                style={{
                  width: isTablet ? 180 : 120,
                  height: isTablet ? 56 : 40,
                  backgroundColor: '#0E7E61',
                  borderRadius: 99,
                  justifyContent: 'center',
                  alignItems: 'center',
                  margin: isTablet ? 16 : 8
                }}
              >
                <Text
                  style={{
                    color: 'white',
                    fontFamily: 'MuktaVaaniSemiBold',
                    fontWeight: '600',
                    fontSize: isTablet ? 20 : 14,
                    textAlign: 'center',
                  }}
                >
                  Wear It
                </Text>
              </TouchableOpacity>
            </ButtonContainer>

            {/* Stats Table */}
            <TableContainer width={contentWidth}>
              <TableTitle>Clothing Stats</TableTitle>
              <StatsTable width={contentWidth}>
                <TableRow height={isTablet ? 48 : 36} style={{ marginTop: 0 }}>
                  <TableCell>
                    <CellContent>
                      <CellTitle numberOfLines={1}>Last Registered Wear</CellTitle>
                    </CellContent>
                  </TableCell>
                  <TableCell>
                    <CellContent>
                      <CellText numberOfLines={1}>{item.lastWorn ? new Date(item.lastWorn).toLocaleDateString('en-US', { day: 'numeric', month: 'long', year: 'numeric' }) : 'Not worn yet'}</CellText>
                    </CellContent>
                  </TableCell>
                </TableRow>

                <TableRow height={isTablet ? 48 : 36}>
                  <TableCell>
                    <CellContent>
                      <CellTitle numberOfLines={1}>Rewear Record</CellTitle>
                    </CellContent>
                  </TableCell>
                  <TableCell>
                    <CellContent>
                      <CellText numberOfLines={1}>
                        {item.wearCount && item.wearCount > 0
                          ? `Worn ${item.wearCount} ${item.wearCount === 1 ? 'time' : 'times'}`
                          : 'Not worn yet'}
                      </CellText>
                    </CellContent>
                  </TableCell>
                </TableRow>

                <TableRow height={isTablet ? 48 : 36}>
                  <TableCell>
                    <CellContent>
                      <CellTitle numberOfLines={1}>Included in Packing Lists</CellTitle>
                    </CellContent>
                  </TableCell>
                  <TableCell>
                    <CellContent>
                      <CellText numberOfLines={1}>
                        {item.packingLists && item.packingLists.length > 0
                          ? item.packingLists.map((list: string, index: number) =>
                              `${list}${index < item.packingLists.length - 1 ? ', ' : ''}`
                            ).join('')
                          : 'US Trip'}
                      </CellText>
                    </CellContent>
                  </TableCell>
                </TableRow>

                <TableRow height={isTablet ? 48 : 36}>
                  <TableCell>
                    <CellContent>
                      <CellTitle numberOfLines={1}>Added</CellTitle>
                    </CellContent>
                  </TableCell>
                  <TableCell>
                    <CellContent>
                      <CellText numberOfLines={1}>
                        {item.createdAt
                          ? new Date(item.createdAt).toLocaleDateString('en-US', { day: 'numeric', month: 'long', year: 'numeric' })
                          : 'Recently added'}
                      </CellText>
                    </CellContent>
                  </TableCell>
                </TableRow>

                <TableRow height={isTablet ? 48 : 36}>
                  <TableCell>
                    <CellContent>
                      <CellTitle numberOfLines={1}>Category</CellTitle>
                    </CellContent>
                  </TableCell>
                  <TableCell>
                    <CellContent>
                      <CellText numberOfLines={1}>
                        {/* Display user-friendly category name */}
                        {(() => {
                          // If category object with name exists, use that
                          if (item.category?.name) return item.category.name;

                          // If we have an itemCategoryId, map it to a user-friendly name
                          if (item.itemCategoryId) {
                            // Map of common category IDs to names
                            const categoryMap: {[key: string]: string} = {
                              'tops': 'Tops',
                              'bottoms': 'Bottoms',
                              'shoes': 'Shoes',
                              'dresses': 'Dresses',
                              'accessories': 'Accessories'
                            };

                            // Check if the ID is in our map
                            for (const [key, value] of Object.entries(categoryMap)) {
                              if (item.itemCategoryId.toLowerCase().includes(key)) {
                                return value;
                              }
                            }
                          }

                          // Fallback to a generic category based on the item name
                          if (item.name) {
                            const name = item.name.toLowerCase();
                            if (name.includes('shoe') || name.includes('sneaker') || name.includes('boot')) {
                              return 'Shoes';
                            } else if (name.includes('pant') || name.includes('jean') || name.includes('skirt')) {
                              return 'Bottoms';
                            } else if (name.includes('shirt') || name.includes('top') || name.includes('blouse')) {
                              return 'Tops';
                            }
                          }

                          return 'Clothing Item';
                        })()}
                      </CellText>
                    </CellContent>
                  </TableCell>
                </TableRow>

                <TableRow height={isTablet ? 48 : 36}>
                  <TableCell>
                    <CellContent>
                      <CellTitle numberOfLines={1}>Brand</CellTitle>
                    </CellContent>
                  </TableCell>
                  <TableCell>
                    <CellContent>
                      <CellText numberOfLines={1}>
                        {item.brand || 'Unknown'}
                      </CellText>
                    </CellContent>
                  </TableCell>
                </TableRow>

                <TableRow height={isTablet ? 48 : 36}>
                  <TableCell>
                    <CellContent>
                      <CellTitle numberOfLines={1}>Color</CellTitle>
                    </CellContent>
                  </TableCell>
                  <TableCell>
                    <CellContent>
                      <CellText numberOfLines={1}>
                        {item.color || 'Not specified'}
                      </CellText>
                    </CellContent>
                  </TableCell>
                </TableRow>
              </StatsTable>
            </TableContainer>

            {/* Outfits Section */}
            <OutfitContainer width={contentWidth}>
              <OutfitTitle>Outfits You've Included it in</OutfitTitle>
              <OutfitCardContainer width={contentWidth} cardSize={cardSize}>
                {item.outfits && item.outfits.length > 0 ? (
                  // Render actual outfits if available
                  item.outfits.slice(0, 3).map((outfit: any, index: number) => {
                    // Optimize the image URL for faster loading
                    const outfitImageUrl = optimizeImageUrl(outfit.imageUrl || null);

                    return (
                      <OutfitCard key={outfit._id || `outfit-${index}`} size={cardSize}>
                        <OutfitImage
                          size={cardSize}
                          source={outfitImageUrl
                            ? { uri: outfitImageUrl }
                            : index % 2 === 0
                              ? require('../../../assets/images/closet/closet-outfit-main2.png')
                              : require('../../../assets/images/closet/closet-outfit-main3.png')
                          }
                          placeholder={PLACEHOLDER_BLURHASH}
                          contentFit="cover"
                          transition={200}
                          cachePolicy="memory-disk"
                          recyclingKey={`outfit-${outfit._id || index}`}
                          onLoadStart={() => {
                            if (isMounted.current) {
                              setOutfitImagesLoading(prev => ({ ...prev, [`outfit-${outfit._id || index}`]: true }));
                            }
                          }}
                          onLoad={() => {
                            if (isMounted.current) {
                              setTimeout(() => {
                                setOutfitImagesLoading(prev => ({ ...prev, [`outfit-${outfit._id || index}`]: false }));
                              }, 100);
                            }
                          }}
                          onError={() => {
                            if (isMounted.current) {
                              setOutfitImagesLoading(prev => ({ ...prev, [`outfit-${outfit._id || index}`]: false }));
                            }
                          }}
                        />
                        {outfitImagesLoading[`outfit-${outfit._id || index}`] && (
                          <ImageLoadingContainer>
                            <ActivityIndicator size="small" color="#0E7E61" />
                          </ImageLoadingContainer>
                        )}
                      </OutfitCard>
                    );
                  })
                ) : (
                  // Fallback to placeholder outfits
                  <>
                    <OutfitCard size={cardSize}>
                      <OutfitImage
                        size={cardSize}
                        source={require('../../../assets/images/closet/closet-outfit-main3.png')}
                        placeholder={PLACEHOLDER_BLURHASH}
                        contentFit="cover"
                        transition={200}
                        cachePolicy="memory-disk"
                        recyclingKey="outfit-placeholder-1"
                        onLoadStart={() => {
                          if (isMounted.current) {
                            setOutfitImagesLoading(prev => ({ ...prev, 'outfit-placeholder-1': true }));
                          }
                        }}
                        onLoad={() => {
                          if (isMounted.current) {
                            setTimeout(() => {
                              setOutfitImagesLoading(prev => ({ ...prev, 'outfit-placeholder-1': false }));
                            }, 100);
                          }
                        }}
                      />
                      {outfitImagesLoading['outfit-placeholder-1'] && (
                        <ImageLoadingContainer>
                          <ActivityIndicator size="small" color="#0E7E61" />
                        </ImageLoadingContainer>
                      )}
                    </OutfitCard>
                    <OutfitCard size={cardSize}>
                      <OutfitImage
                        size={cardSize}
                        source={require('../../../assets/images/closet/closet-outfit-main2.png')}
                        placeholder={PLACEHOLDER_BLURHASH}
                        contentFit="cover"
                        transition={200}
                        cachePolicy="memory-disk"
                        recyclingKey="outfit-placeholder-2"
                        onLoadStart={() => {
                          if (isMounted.current) {
                            setOutfitImagesLoading(prev => ({ ...prev, 'outfit-placeholder-2': true }));
                          }
                        }}
                        onLoad={() => {
                          if (isMounted.current) {
                            setTimeout(() => {
                              setOutfitImagesLoading(prev => ({ ...prev, 'outfit-placeholder-2': false }));
                            }, 100);
                          }
                        }}
                      />
                      {outfitImagesLoading['outfit-placeholder-2'] && (
                        <ImageLoadingContainer>
                          <ActivityIndicator size="small" color="#0E7E61" />
                        </ImageLoadingContainer>
                      )}
                    </OutfitCard>
                    <OutfitCard size={cardSize}>
                      <OutfitImage
                        size={cardSize}
                        source={require('../../../assets/images/closet/closet-outfit-main3.png')}
                        placeholder={PLACEHOLDER_BLURHASH}
                        contentFit="cover"
                        transition={200}
                        cachePolicy="memory-disk"
                        recyclingKey="outfit-placeholder-3"
                        onLoadStart={() => {
                          if (isMounted.current) {
                            setOutfitImagesLoading(prev => ({ ...prev, 'outfit-placeholder-3': true }));
                          }
                        }}
                        onLoad={() => {
                          if (isMounted.current) {
                            setTimeout(() => {
                              setOutfitImagesLoading(prev => ({ ...prev, 'outfit-placeholder-3': false }));
                            }, 100);
                          }
                        }}
                      />
                      {outfitImagesLoading['outfit-placeholder-3'] && (
                        <ImageLoadingContainer>
                          <ActivityIndicator size="small" color="#0E7E61" />
                        </ImageLoadingContainer>
                      )}
                    </OutfitCard>
                  </>
                )}
              </OutfitCardContainer>
            </OutfitContainer>

            {/* Suggested Section */}
            <SuggestedContainer width={contentWidth}>
              <SuggestedTitle>Suggested Pairings</SuggestedTitle>
              <SuggestedCardContainer width={contentWidth} cardSize={cardSize}>
                {item.suggestedPairings && item.suggestedPairings.length > 0 ? (
                  // Render actual suggested pairings if available
                  item.suggestedPairings.slice(0, 3).map((pairing: any, index: number) => {
                    // Optimize the image URL for faster loading
                    const pairingImageUrl = optimizeImageUrl(pairing.imageUrl || null);

                    return (
                      <SuggestedCard key={pairing._id || `pairing-${index}`} size={cardSize}>
                        <SuggestedImage
                          size={cardSize}
                          source={pairingImageUrl
                            ? { uri: pairingImageUrl }
                            : placeholderImage
                          }
                          placeholder={PLACEHOLDER_BLURHASH}
                          contentFit="cover"
                          transition={200}
                          cachePolicy="memory-disk"
                          recyclingKey={`pairing-${pairing._id || index}`}
                          onLoadStart={() => {
                            if (isMounted.current) {
                              setSuggestedImagesLoading(prev => ({ ...prev, [`pairing-${pairing._id || index}`]: true }));
                            }
                          }}
                          onLoad={() => {
                            if (isMounted.current) {
                              setTimeout(() => {
                                setSuggestedImagesLoading(prev => ({ ...prev, [`pairing-${pairing._id || index}`]: false }));
                              }, 100);
                            }
                          }}
                          onError={() => {
                            if (isMounted.current) {
                              setSuggestedImagesLoading(prev => ({ ...prev, [`pairing-${pairing._id || index}`]: false }));
                            }
                          }}
                        />
                        {suggestedImagesLoading[`pairing-${pairing._id || index}`] && (
                          <ImageLoadingContainer>
                            <ActivityIndicator size="small" color="#0E7E61" />
                          </ImageLoadingContainer>
                        )}
                      </SuggestedCard>
                    );
                  })
                ) : (
                  // Fallback to placeholder suggested pairings
                  <>
                    <SuggestedCard size={cardSize}>
                      <SuggestedImage
                        size={cardSize}
                        source={placeholderImage}
                        placeholder={PLACEHOLDER_BLURHASH}
                        contentFit="cover"
                        transition={200}
                        cachePolicy="memory-disk"
                        recyclingKey="suggested-placeholder-1"
                        onLoadStart={() => {
                          if (isMounted.current) {
                            setSuggestedImagesLoading(prev => ({ ...prev, 'suggested-placeholder-1': true }));
                          }
                        }}
                        onLoad={() => {
                          if (isMounted.current) {
                            setTimeout(() => {
                              setSuggestedImagesLoading(prev => ({ ...prev, 'suggested-placeholder-1': false }));
                            }, 100);
                          }
                        }}
                      />
                      {suggestedImagesLoading['suggested-placeholder-1'] && (
                        <ImageLoadingContainer>
                          <ActivityIndicator size="small" color="#0E7E61" />
                        </ImageLoadingContainer>
                      )}
                    </SuggestedCard>
                    <SuggestedCard size={cardSize}>
                      <SuggestedImage
                        size={cardSize}
                        source={placeholderImage}
                        placeholder={PLACEHOLDER_BLURHASH}
                        contentFit="cover"
                        transition={200}
                        cachePolicy="memory-disk"
                        recyclingKey="suggested-placeholder-2"
                        onLoadStart={() => {
                          if (isMounted.current) {
                            setSuggestedImagesLoading(prev => ({ ...prev, 'suggested-placeholder-2': true }));
                          }
                        }}
                        onLoad={() => {
                          if (isMounted.current) {
                            setTimeout(() => {
                              setSuggestedImagesLoading(prev => ({ ...prev, 'suggested-placeholder-2': false }));
                            }, 100);
                          }
                        }}
                      />
                      {suggestedImagesLoading['suggested-placeholder-2'] && (
                        <ImageLoadingContainer>
                          <ActivityIndicator size="small" color="#0E7E61" />
                        </ImageLoadingContainer>
                      )}
                    </SuggestedCard>
                  </>
                )}
              </SuggestedCardContainer>
            </SuggestedContainer>
          </ContentContainer>
        )}
      </Container>

      {/* Wear It Modal */}
      <WearItModal
        isVisible={isWearItModalVisible}
        onClose={() => setIsWearItModalVisible(false)}
        itemId={id}
        onSave={(wearData) => {
          console.log('Wear data:', wearData);

          // Update the wear count in the backend
          wearItemMutation.mutate(id, {
            onSuccess: () => {
              console.log('Wear count updated successfully');
              // Show success message
              Alert.alert('Success', 'Item marked as worn!');
            },
            onError: (error) => {
              console.error('Error updating wear count:', error);
              // Show error message
              Alert.alert('Error', 'Failed to update wear count. Please try again.');
            }
          });

          // Close the modal
          setIsWearItModalVisible(false);
        }}
      />

      {/* Delete Options Modal */}
      <DeleteOptionsModal
        isVisible={isDeleteOptionsVisible}
        onClose={() => setIsDeleteOptionsVisible(false)}
        onDeletePress={handleDeletePress}
      />

      {/* Delete Confirmation Modal */}
      <DeleteItemModal
        isVisible={isDeleteModalVisible}
        onClose={() => setIsDeleteModalVisible(false)}
        onDelete={confirmDeleteItem}
        itemName={item?.name || 'this item'}
        isLoading={isDeleting}
      />
    </SafeArea>
  );
}
